from knowledge import KnowledgeRepo
import time
from poc import RecommendationAgent
import streamlit as st
import pandas as pd
import json
import uuid
from datetime import datetime

st.set_page_config(page_title="Skin Survey Analysis", page_icon="🧴", layout="wide")

if "survey_data" not in st.session_state:
    st.session_state.survey_data = {}
if "current_step" not in st.session_state:
    st.session_state.current_step = 0
if "survey_results" not in st.session_state:
    st.session_state.survey_results = []
if "skin_analysis_data" not in st.session_state:
    st.session_state.skin_analysis_data = {}

# Initialize default values for skin analysis data
if not st.session_state.skin_analysis_data:
    skin_analysis_fields = {
        "rgb_pore": "RGB Pore Analysis",
        "rgb_spot": "RGB Spot Analysis",
        "rgb_wrinkle": "RGB Wrinkle Analysis",
        "pl_texture": "PL Texture Analysis",
        "uv_porphyrin": "UV Porphyrin Analysis",
        "uv_pigmentation": "UV Pigmentation Analysis",
        "uv_moisture": "UV Moisture Analysis",
        "sensitive_area": "Sensitive Area Analysis",
        "brown_area": "Brown Area Analysis",
        "uv_damage": "UV Damage Analysis"
    }
    for field in skin_analysis_fields.keys():
        st.session_state.skin_analysis_data[field] = 40.0


@st.cache_data
def load_survey_data():
    try:
        df = pd.read_csv("survey_questions.csv")

        df = df.sort_values("question_order")

        return df
    except Exception as e:
        st.error(f"Error loading survey data: {e}")
        return pd.DataFrame()


def parse_answer_options(answers_str):
    """Parse answer options from JSON string"""
    try:
        if pd.isna(answers_str) or answers_str == "":
            return []

        clean_str = answers_str.replace("'", '"').replace('""', '"')
        answers = json.loads(clean_str)
        return [answer["title"] for answer in answers]
    except Exception as e:
        st.error(f"Error parsing answers: {e} for string: {answers_str}")
        return []


def get_main_questions(df):
    """Get main questions (no parent_question_id)"""
    return df[df["parent_question_id"].isna() | (df["parent_question_id"] == "")].copy()


def get_child_questions(df, parent_id, parent_answer_index):
    """Get child questions based on parent question and selected answer"""
    return df[
        (df["parent_question_id"] == parent_id)
        & (df["parent_question_answer"] == parent_answer_index)
    ].copy()


def render_skin_analysis_inputs():
    """Render number input fields for skin analysis parameters"""
    st.subheader("🔬 Skin Analysis Parameters")
    st.write("Please enter the skin analysis values:")

    # Define the skin analysis fields with their descriptions
    skin_analysis_fields = {
        "rgb_pore": "RGB Pore Analysis",
        "rgb_spot": "RGB Spot Analysis",
        "rgb_wrinkle": "RGB Wrinkle Analysis",
        "pl_texture": "PL Texture Analysis",
        "uv_porphyrin": "UV Porphyrin Analysis",
        "uv_pigmentation": "UV Pigmentation Analysis",
        "uv_moisture": "UV Moisture Analysis",
        "sensitive_area": "Sensitive Area Analysis",
        "brown_area": "Brown Area Analysis",
        "uv_damage": "UV Damage Analysis"
    }

    # Create two columns for better layout
    col1, col2 = st.columns(2)

    skin_analysis_data = {}

    with col1:
        for i, (field, description) in enumerate(skin_analysis_fields.items()):
            if i < 5:  # First 5 fields in left column
                current_value = st.session_state.skin_analysis_data.get(field, 40.0)
                value = st.number_input(
                    f"{description}:",
                    min_value=0.0,
                    max_value=100.0,
                    value=current_value,
                    step=0.1,
                    key=f"skin_analysis_{field}"
                )
                skin_analysis_data[field] = value

    with col2:
        for i, (field, description) in enumerate(skin_analysis_fields.items()):
            if i >= 5:  # Last 5 fields in right column
                current_value = st.session_state.skin_analysis_data.get(field, 40.0)
                value = st.number_input(
                    f"{description}:",
                    min_value=0.0,
                    max_value=100.0,
                    value=current_value,
                    step=0.1,
                    key=f"skin_analysis_{field}"
                )
                skin_analysis_data[field] = value

    return skin_analysis_data


def render_question(question_row, question_key):
    """Render a single question based on its type"""
    question_text = question_row["question"]
    answer_options = parse_answer_options(question_row["answers"])
    is_multiple = question_row["is_multiple"]
    question_type = question_row["type"]

    st.write(f"**{question_text}**")

    if question_row["description"]:
        st.write(f"*{question_row['description']}*")

    selected_answers = []

    if question_type == "dropdown":
        if "treatment-interval" in answer_options:
            interval_options = ["Tidak pernah", "1-2 bulan lalu", "3-4 bulan lalu", "5-6 bulan lalu"]
            # Get pre-selected value from session state
            pre_selected = st.session_state.survey_data.get(question_key, ["1-2 bulan lalu"])
            default_index = interval_options.index(pre_selected[0]) if pre_selected and pre_selected[0] in interval_options else 1
            selected = st.selectbox(
                "Pilih interval:",
                interval_options,
                index=default_index,
                key=question_key,
            )
            if selected:
                selected_answers = [selected]

    elif is_multiple:
        st.write("Pilih semua yang sesuai:")
        for i, option in enumerate(answer_options):
            if st.checkbox(option, key=f"{question_key}_{i}"):
                selected_answers.append(option)

    else:
        # Get pre-selected value from session state
        pre_selected = st.session_state.survey_data.get(question_key, [])
        if pre_selected and pre_selected[0] in answer_options:
            default_index = answer_options.index(pre_selected[0])
        else:
            # Set default to 2nd option (index 1) if available, otherwise None
            default_index = 1 if len(answer_options) > 1 else None

        selected = st.radio(
            "Pilih salah satu:", answer_options, key=question_key, index=default_index
        )
        if selected:
            selected_answers = [selected]

    return selected_answers


def handle_area_selection(main_concerns, question_key):
    """Handle area selection for skin concerns"""
    area_options = [
        "Area 1 (Dahi-Mata)",
        "Area 2 (Mata-Mulut)",
        "Area 3 (Mulut-Dagu)",
        "All Face Area",
    ]

    selected_with_areas = []

    for concern in main_concerns:
        st.write(f"**Pilih area untuk {concern}:**")
        concern_areas = []
        for area in area_options:
            if st.checkbox(f"{area}", key=f"{question_key}_{concern}_{area}"):
                concern_areas.append(area)

        if concern_areas:
            for area in concern_areas:
                if area == "All Face Area":
                    selected_with_areas.append(f"{concern} - All Face Area")
                else:
                    area_name = area.split(" (")[1].replace(")", "").replace("-", " - ")
                    selected_with_areas.append(f"{concern} - {area_name}")

    return selected_with_areas


def map_skin_analysis_to_concerns(skin_analysis_data):
    """Map skin analysis scores to skin concerns based on skin problem indications"""

    # Mapping of skin analysis fields to skin problem indications
    skin_analysis_to_indications = {
        "rgb_pore": ["Pore"],
        "rgb_spot": ["Spot"],
        "rgb_wrinkle": ["Wrinkle"],
        "pl_texture": ["Texture"],
        "uv_porphyrin": ["Porphyrin"],
        "uv_pigmentation": ["Pigmentation"],
        "uv_moisture": ["Moisture"],
        "sensitive_area": ["Sensitive Area"],
        "brown_area": ["Brown Area"],
        "uv_damage": ["Uv Damage"]
    }

    # Mapping of skin problem indications to skin concerns (problems)
    indications_to_concerns = {
        "Pore": ["Pore"],
        "Porphyrin": ["Pore", "Scar", "Acne"],
        "Pigmentation": ["Hyperpigmentasi"],
        "Spot": ["Hyperpigmentasi"],
        "Brown Area": ["Hyperpigmentasi"],
        "Uv Damage": ["Hyperpigmentasi"],
        "Wrinkle": ["Wrinkle"],
        "Texture": ["Scar", "Wrinkle", "Acne"],
        "Moisture": ["Sensitive"],
        "Sensitive Area": ["Sensitive"]
    }

    # Create reverse mapping from skin analysis to concerns
    skin_analysis_to_concerns = {}
    for analysis_field, indications in skin_analysis_to_indications.items():
        concerns = set()
        for indication in indications:
            if indication in indications_to_concerns:
                concerns.update(indications_to_concerns[indication])
        skin_analysis_to_concerns[analysis_field] = list(concerns)

    return skin_analysis_to_concerns


def generate_survey_json():
    """Generate the final JSON structure"""
    top_concerns = []

    # Collect concerns from multiple question IDs
    skin_concern_keys = [
        "q_2097dda1-544a-4d7a-89c6-6314bcefa592",  # Skin type
        "q_959ac8b0-b1a7-4da6-beaf-70063c83fc53",  # Skin problems
        "q_94e22537-8ff3-4702-ac26-87c0a60139cb",  # Specific concerns
        "q_cef51648-1f44-43dd-944f-a677d37f8db4"   # Other goals
    ]

    # Answers to skip
    skip_answers = ["Normal", "Combination", "Tidak"]

    for skin_concern_key in skin_concern_keys:
        if skin_concern_key in st.session_state.survey_data:
            concerns = st.session_state.survey_data[skin_concern_key]
            for concern in concerns:
                # Skip if the concern is in the skip list
                if concern in skip_answers:
                    continue

                if " - " in concern:
                    top_concerns.append(concern.split(" - ")[0])
                else:
                    top_concerns.append(concern)

    seen = set()
    top_concerns = [x for x in top_concerns if not (x in seen or seen.add(x))]

    results = []
    for key, answers in st.session_state.survey_data.items():
        if key.startswith("q_"):
            question_id = key.replace("q_", "")
            df = load_survey_data()
            question_row = df[df["id"] == question_id]
            if not question_row.empty:
                question_text = question_row.iloc[0]["question"]
                results.append(
                    {
                        "question": question_text,
                        "answers": answers if isinstance(answers, list) else [answers],
                    }
                )

    current_time = int(datetime.now().timestamp() * 1000)

    # Get skin analysis data from session state
    skin_analysis_data = st.session_state.get("skin_analysis_data", {})

    # Find top 3 lowest scores and map them to concerns, then merge into top_concerns
    if skin_analysis_data:
        # Get the mapping from skin analysis to concerns
        skin_analysis_to_concerns = map_skin_analysis_to_concerns(skin_analysis_data)

        # Sort the skin analysis data by values (ascending order)
        sorted_analysis = sorted(skin_analysis_data.items(), key=lambda x: x[1])
        # Get the top 3 lowest scores
        top_3_lowest_scores = sorted_analysis[:3]

        # Map each lowest score to its corresponding concerns
        lowest_concerns = []
        for field_name, score in top_3_lowest_scores:
            if field_name in skin_analysis_to_concerns:
                concerns = skin_analysis_to_concerns[field_name]
                lowest_concerns.extend(concerns)

        # Merge lowest concerns into top_concerns
        top_concerns.extend(lowest_concerns)

        # Remove duplicates while preserving order
        seen_concerns = set()
        unique_concerns = []
        for concern in top_concerns:
            if concern not in seen_concerns:
                seen_concerns.add(concern)
                unique_concerns.append(concern)

        top_concerns = unique_concerns


    print(top_concerns)

    survey_json = {
        "top_concern": top_concerns,
        "skin_analysis": skin_analysis_data,
        "user_survey": {
            "id": str(uuid.uuid4()),
            "user_id": None,
            "skin_analyze_id": str(uuid.uuid4()),
            "results": results,
            "created_by": None,
            "created_at": current_time,
            "updated_by": None,
            "updated_at": current_time,
        },
    }

    return survey_json


def initialize_default_survey_answers(df):
    """Initialize default survey answers (2nd option for each question)"""
    if st.session_state.survey_data:
        return  # Already has data, don't reinitialize

    main_questions = get_main_questions(df)

    for _, question_row in main_questions.iterrows():
        question_id = question_row["id"]
        question_key = f"q_{question_id}"
        answer_options = parse_answer_options(question_row["answers"])

        # Set default to 2nd option (index 1) if available
        if len(answer_options) > 1:
            default_answer = answer_options[1]  # 2nd option
            st.session_state.survey_data[question_key] = [default_answer]

            # Handle child questions if this is a single choice question
            if not question_row["is_multiple"]:
                child_questions = get_child_questions(df, question_id, 1)  # index 1 for 2nd option
                for _, child_row in child_questions.iterrows():
                    child_key = f"q_{child_row['id']}"
                    child_answer_options = parse_answer_options(child_row["answers"])
                    if len(child_answer_options) > 1:
                        child_default_answer = child_answer_options[1]  # 2nd option
                        st.session_state.survey_data[child_key] = [child_default_answer]


def main():
    st.title("🧴 Skin Survey Analysis")
    st.write("Complete the survey to get personalized skincare recommendations")

    df = load_survey_data()

    if df.empty:
        st.error(
            "Failed to load survey data. Please check if 'survey_questions.csv' exists."
        )
        return

    # Initialize default survey answers
    initialize_default_survey_answers(df)

    main_questions = get_main_questions(df)
    total_questions = len(main_questions) + 1

    first_question_row = main_questions.iloc[0]
    first_question_id = first_question_row["id"]
    first_question_key = f"q_{first_question_id}"

    with st.expander(f"Question 1: {first_question_row['question']}", expanded=True):
        selected_answers = render_question(first_question_row, first_question_key)
        if selected_answers:
            st.session_state.survey_data[first_question_key] = selected_answers

            if "Ya" in selected_answers:
                st.warning(
                    "Anda tidak dapat melanjutkan survei berdasarkan jawaban Anda."
                )
                return

    progress = (
        len(st.session_state.survey_data) / total_questions
        if total_questions > 0
        else 0
    )
    st.progress(progress)
    st.write(
        f"Progress: {len(st.session_state.survey_data)}/{total_questions} questions completed"
    )

    for idx in range(1, len(main_questions)):
        question_row = main_questions.iloc[idx]
        question_id = question_row["id"]
        question_key = f"q_{question_id}"

        with st.expander(
            f"Question {idx + 1}: {question_row['question']}", expanded=True
        ):
            selected_answers = render_question(question_row, question_key)

            if (
                question_id == "959ac8b0-b1a7-4da6-beaf-70063c83fc53"
                and selected_answers
            ):
                st.write("---")
                st.write("**Pilih area yang bermasalah:**")
                area_answers = handle_area_selection(
                    selected_answers, f"{question_key}_areas"
                )
                if area_answers:
                    selected_answers = area_answers

            if selected_answers and question_row["is_multiple"] == False:
                selected_index = 0
                if len(selected_answers) > 0:
                    answer_options = parse_answer_options(question_row["answers"])
                    try:
                        selected_index = answer_options.index(selected_answers[0])
                    except ValueError:
                        selected_index = 0

                child_questions = get_child_questions(df, question_id, selected_index)

                for _, child_row in child_questions.iterrows():
                    child_key = f"q_{child_row['id']}"
                    st.write("---")
                    child_answers = render_question(child_row, child_key)
                    if child_answers:
                        st.session_state.survey_data[child_key] = child_answers

            if selected_answers:
                st.session_state.survey_data[question_key] = selected_answers

        st.write("---")

    # Add skin analysis section after all questions
    with st.expander("🔬 Skin Analysis Parameters", expanded=True):
        skin_analysis_data = render_skin_analysis_inputs()
        st.session_state.skin_analysis_data = skin_analysis_data

    if len(st.session_state.survey_data) > 0:
        st.subheader("📊 Survey Results")

        col1, col2 = st.columns(2)

        with col1:
            st.write("**Current Responses:**")
            for key, value in st.session_state.survey_data.items():
                if key.startswith("q_"):
                    if key == "q_top_concern":
                        st.write("• Top Skin Concerns")
                    else:
                        question_id = key.replace("q_", "")
                        question_row = df[df["id"] == question_id]
                        if not question_row.empty:
                            question_text = question_row.iloc[0]["question"]
                            st.write(f"• {question_text}")
                    for answer in value if isinstance(value, list) else [value]:
                        st.write(f"  - {answer}")

            # Display skin analysis data
            if "skin_analysis_data" in st.session_state:
                st.write("**Skin Analysis Data:**")
                for field, value in st.session_state.skin_analysis_data.items():
                    st.write(f"• {field}: {value}")

        with col2:
            if st.button("Generate Treatments", type="primary"):
                start_time = time.time()
                survey_json = generate_survey_json()

                OPENAI_API_KEY = "********************************************************************************************************************************************************************"

                knowledge = KnowledgeRepo()
                agent_repo = RecommendationAgent(knowledge, OPENAI_API_KEY)

                response_str = agent_repo.get_treatment_recommendations(survey_json)
                response = json.loads(response_str)
                end_time = time.time()
                execution_time = end_time - start_time

                st.info(f"⏱️ Execution Time: {execution_time:.2f} seconds")
                if response["status"] == "success":
                    data = response["data"]
                    st.success("Recommendations generated successfully!")

                    st.subheader("Summary")
                    st.write(data["summary"])

                    top_recommendations = [
                        t for t in data["treatments"] if t["is_top_recommendation"]
                    ]
                    other_recommendations = [
                        t for t in data["treatments"] if not t["is_top_recommendation"]
                    ]

                    def render_treatment(t):
                        st.markdown(f"### 💆 {t['name']}")
                        st.write(t["description"])
                        st.write(f"💸 **Price**: Rp {t['price']:,}")
                        st.write(f"🔁 **Quantity**: {t['quantity']}")
                        st.write(f"🏷️ **Categories**: {', '.join(t['categories'])}")
                        st.write(
                            f"🎯 **Solved Concerns**: {', '.join(t['solved_concerns'])}"
                        )
                        st.markdown("---")

                    if top_recommendations:
                        st.subheader("⭐ Top Recommendations")
                        for t in top_recommendations:
                            render_treatment(t)

                    if other_recommendations:
                        st.subheader("📝 Other Recommendations")
                        for t in other_recommendations:
                            render_treatment(t)
                else:
                    st.error("Failed to get treatment recommendations.")
                    print(response)

        if st.button("🔄 Reset Survey", type="secondary"):
            st.session_state.survey_data = {}
            st.session_state.current_step = 0
            st.session_state.survey_results = []
            st.session_state.skin_analysis_data = {}  # Reset skin analysis data
            st.rerun()


if __name__ == "__main__":
    main()
